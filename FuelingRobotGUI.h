#ifndef FUELINGROBOTGUI_H
#define FUELINGROBOTGUI_H

#include <QMainWindow>
#include <QPushButton>
#include <QLabel>
#include <QComboBox>
#include <QSpinBox>
#include <QGroupBox>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QStatusBar>
#include <QThread>
#include <QMessageBox>
#include <QTextEdit> // 新增：包含QTextEdit头文件
#include "SystemManager.h"
#include <iostream> // 新增：包含iostream

// 自定义streambuf，用于重定向cout输出到Qt界面
class LogBuffer : public std::streambuf
{
public:
    LogBuffer(std::ostream &os, std::function<void(const QString&)> logCallback)
        : m_os(os), m_logCallback(logCallback)
    {
        m_oldBuf = os.rdbuf(this);
    }

    ~LogBuffer()
    {
        m_os.rdbuf(m_oldBuf);
    }

protected:
    virtual int_type overflow(int_type c) override
    {
        if (c != EOF) {
            m_buffer += static_cast<char>(c);
            if (c == '\n') {
                emitLog();
            }
        }
        return c;
    }

    virtual std::streamsize xsputn(const char *s, std::streamsize n) override
    {
        m_buffer.append(s, n);
        if (m_buffer.find('\n') != std::string::npos) {
            emitLog();
        }
        return n;
    }

private:
    void emitLog()
    {
        if (m_logCallback) {
            m_logCallback(QString::fromStdString(m_buffer));
        }
        m_buffer.clear();
    }

    std::ostream &m_os;
    std::streambuf *m_oldBuf;
    std::string m_buffer;
    std::function<void(const QString&)> m_logCallback;
};

// 工作线程类，用于在后台运行系统管理器
class WorkerThread : public QThread
{
    Q_OBJECT

public:
    WorkerThread(SystemManager* sysManager) : m_sysManager(sysManager) {}

    void run() override {
        if (m_sysManager) {
            int result = m_sysManager->startup();
            emit operationFinished(result);
        }
    }

signals:
    void operationFinished(int result);

private:
    SystemManager* m_sysManager;
};

// 主窗口类
class FuelingRobotGUI : public QMainWindow
{
    Q_OBJECT

public:
    FuelingRobotGUI(QWidget *parent = nullptr);
    ~FuelingRobotGUI();

private slots:
    void onStartButtonClicked();
    void onStopButtonClicked();
    void onOperationFinished(int result);
    void appendLogMessage(const QString& message); // 新增：显示日志的槽函数
    void onSingleImgProcessButtonClicked(); // 新增：单张图像处理按钮点击槽函数
    void onAreaMoveButtonClicked(); // 新增：区域移动按钮点击槽函数

private:
    void setupUI();
    void createConnections();
    void updateStatus(const QString& message);

private:
    // UI组件
    QPushButton* m_startButton;
    QPushButton* m_stopButton;
    QComboBox* m_areaSelector;
    QComboBox* m_oilTypeSelector;
    QSpinBox* m_amountInput;
    QLabel* m_statusLabel;
    QTextEdit* m_logDisplay; // 新增：用于显示日志的文本区域

    // 系统管理器
    SystemManager* m_sysManager;

    // 工作线程
    WorkerThread* m_workerThread;
    std::unique_ptr<LogBuffer> m_logBuffer; // 新增：日志重定向缓冲区
    QPushButton* m_singleImgProcessButton; // 新增：单张图像处理按钮
    QPushButton* m_areaMoveButton; // 新增：区域移动按钮
};

#endif // FUELINGROBOTGUI_H
