#include "FuelingRobotGUI.h"

FuelingRobotGUI::FuelingRobotGUI(QWidget *parent)
    : QMainWindow(parent), m_workerThread(nullptr)
{
    // 创建系统管理器实例
    m_sysManager = new SystemManager();

    // 设置UI
    setupUI();
    createConnections();

    // 重定向cout输出到m_logDisplay
    m_logBuffer = std::make_unique<LogBuffer>(std::cout,
                                              [this](const QString& msg) {
                                                  QMetaObject::invokeMethod(this, "appendLogMessage",
                                                                            Qt::QueuedConnection, Q_ARG(QString, msg));
                                              });

    // 设置窗口标题和大小
    setWindowTitle("加油机器人控制系统");
    resize(800, 600);

    // 更新状态
    updateStatus("系统就绪");
}

FuelingRobotGUI::~FuelingRobotGUI()
{
    // 如果线程正在运行，等待其结束
    if (m_workerThread && m_workerThread->isRunning()) {
        m_sysManager->setCancelTask(true);
        m_workerThread->wait();
    }

    delete m_sysManager;
    delete m_workerThread;
}

void FuelingRobotGUI::setupUI()
{
    // 创建中央部件和布局
    QWidget* centralWidget = new QWidget(this);
    QVBoxLayout* mainLayout = new QVBoxLayout(centralWidget);

    // 创建控制面板
    QGroupBox* controlGroup = new QGroupBox("系统控制", centralWidget);
    QHBoxLayout* controlLayout = new QHBoxLayout(controlGroup);

    m_startButton = new QPushButton("启动系统", controlGroup);
    m_startButton->setIcon(QIcon(":/icons/start.svg"));
    m_stopButton = new QPushButton("停止系统", controlGroup);
    m_stopButton->setIcon(QIcon(":/icons/stop.svg"));

    // 初始状态下，停止按钮禁用
    m_stopButton->setEnabled(false);

    controlLayout->addWidget(m_startButton);
    controlLayout->addWidget(m_stopButton);

    // 创建参数设置面板
    QGroupBox* paramGroup = new QGroupBox("参数设置", centralWidget);
    QGridLayout* paramLayout = new QGridLayout(paramGroup);

    paramLayout->addWidget(new QLabel("加油区域:", paramGroup), 0, 0);
    m_areaSelector = new QComboBox(paramGroup);
    m_areaSelector->addItem("区域0", 0);
    m_areaSelector->addItem("区域1", 1);
    m_areaSelector->addItem("区域2", 2);
    paramLayout->addWidget(m_areaSelector, 0, 1);

    paramLayout->addWidget(new QLabel("油品类型:", paramGroup), 1, 0);
    m_oilTypeSelector = new QComboBox(paramGroup);
    m_oilTypeSelector->addItem("92号油", 92);
    m_oilTypeSelector->addItem("95号油", 95);
    paramLayout->addWidget(m_oilTypeSelector, 1, 1);

    paramLayout->addWidget(new QLabel("加油金额:", paramGroup), 2, 0);
    m_amountInput = new QSpinBox(paramGroup);
    m_amountInput->setRange(50, 500);
    m_amountInput->setSingleStep(50);
    m_amountInput->setValue(100);
    m_amountInput->setSuffix(" 元");
    paramLayout->addWidget(m_amountInput, 2, 1);

    // 创建状态显示区域
    QGroupBox* statusGroup = new QGroupBox("系统状态", centralWidget);
    QVBoxLayout* statusLayout = new QVBoxLayout(statusGroup);

    m_statusLabel = new QLabel("系统未启动", statusGroup);
    m_statusLabel->setAlignment(Qt::AlignCenter);
    m_statusLabel->setFrameStyle(QFrame::Panel | QFrame::Sunken);
    m_statusLabel->setMinimumHeight(100);

    statusLayout->addWidget(m_statusLabel);

    // 新增：日志输出区域
    QGroupBox* logGroup = new QGroupBox("日志输出", centralWidget);
    QVBoxLayout* logLayout = new QVBoxLayout(logGroup);
    m_logDisplay = new QTextEdit(logGroup);
    m_logDisplay->setReadOnly(true); // 设置为只读
    logLayout->addWidget(m_logDisplay);

    // 单张图像处理按钮
    m_singleImgProcessButton = new QPushButton("单张图像处理", logGroup);
    logLayout->addWidget(m_singleImgProcessButton);

    // 区域移动按钮
    m_areaMoveButton = new QPushButton("区域移动", logGroup);
    logLayout->addWidget(m_areaMoveButton);

    mainLayout->addWidget(controlGroup);
    mainLayout->addWidget(paramGroup);
    mainLayout->addWidget(statusGroup);
    mainLayout->addWidget(logGroup); // 添加日志输出分组框
    mainLayout->addStretch();

    // 设置中央部件
    setCentralWidget(centralWidget);

    // 创建状态栏
    statusBar()->showMessage("系统就绪");
}

void FuelingRobotGUI::createConnections()
{
    // 连接开始和停止按钮
    connect(m_startButton, &QPushButton::clicked, this, &FuelingRobotGUI::onStartButtonClicked);
    connect(m_stopButton, &QPushButton::clicked, this, &FuelingRobotGUI::onStopButtonClicked);

    // 连接单张图像处理按钮
    connect(m_singleImgProcessButton, &QPushButton::clicked, this, &FuelingRobotGUI::onSingleImgProcessButtonClicked);

    // 连接区域移动按钮
    connect(m_areaMoveButton, &QPushButton::clicked, this, &FuelingRobotGUI::onAreaMoveButtonClicked);

    // 连接信号，当worker线程完成任务时更新UI
    connect(m_workerThread, &WorkerThread::operationFinished, this, &FuelingRobotGUI::onOperationFinished);
}

void FuelingRobotGUI::onStartButtonClicked()
{
    // 获取参数
    int area = m_areaSelector->currentData().toInt();
    int oilType = m_oilTypeSelector->currentData().toInt();
    int amount = m_amountInput->value();

    // 创建任务
    Task task;
    task.timeStamp = ""; // 可以在这里设置时间戳
    task.code = 1; // 假设1表示加油任务
    task.desc = "GUI触发的加油任务";
    task.oiltype = oilType;
    task.amount = amount;
    task.area = area;

    // 更新UI状态
    m_startButton->setEnabled(false);
    m_stopButton->setEnabled(true);
    m_areaSelector->setEnabled(true);
    m_oilTypeSelector->setEnabled(true);
    m_amountInput->setEnabled(true);

    updateStatus("系统启动中...");

    // 创建并启动工作线程
    if (m_workerThread) {
        delete m_workerThread;
    }

    m_workerThread = new WorkerThread(m_sysManager);
    connect(m_workerThread, &WorkerThread::operationFinished, this, &FuelingRobotGUI::onOperationFinished);

    // 重置取消标志
    m_sysManager->setCancelTask(false);

    // 启动线程
    m_workerThread->start();
}

void FuelingRobotGUI::onStopButtonClicked()
{
    if (m_workerThread && m_workerThread->isRunning()) {
        // 设置取消标志
        m_sysManager->setCancelTask(true);

        updateStatus("正在停止系统...");
        statusBar()->showMessage("正在停止系统...");
    }
}

void FuelingRobotGUI::onOperationFinished(int result)
{
    // 恢复UI状态
    m_startButton->setEnabled(true);
    m_stopButton->setEnabled(false);
    m_areaSelector->setEnabled(false);
    m_oilTypeSelector->setEnabled(false);
    m_amountInput->setEnabled(false);

    if (result == 0) {
        updateStatus("操作完成");
        statusBar()->showMessage("操作完成", 5000);
    }
    else {
        updateStatus("操作失败，错误代码: " + QString::number(result));
        statusBar()->showMessage("操作失败", 5000);
    }
}

void FuelingRobotGUI::appendLogMessage(const QString& message)
{
    m_logDisplay->append(message);
}

void FuelingRobotGUI::updateStatus(const QString& message)
{
    m_statusLabel->setText(message);
}

void FuelingRobotGUI::onSingleImgProcessButtonClicked()
{
    m_sysManager->getImageProcessor().singleImgProcess();
    appendLogMessage("执行单张图像处理...");
}

void FuelingRobotGUI::onAreaMoveButtonClicked()
{
    // 获取当前选择的加油区域
    int area = m_areaSelector->currentData().toInt();

    // 构造任务字符串
    QString taskStr = QString("Move_Area_%1").arg(area);

    // 调用SystemManager的addTask函数
    m_sysManager->addTask(taskStr.toStdString());

    // 显示日志信息
    appendLogMessage(QString("添加区域移动任务: %1").arg(taskStr));
}
